# 认证模块前端实现指导

## 🎯 实现目标

基于Flutter + GetX实现用户认证模块的前端功能，与现有后端API完美集成。本文档提供完整的代码级实现指导，AI可以直接根据此文档生成可用的前端代码。

## 📋 需求驱动映射

### 业务需求映射
- **BR-002**: 数据安全和隐私 → 本地数据加密、生物识别
- **BR-003**: 多设备无缝体验 → 设备管理界面、状态同步
- **BR-004**: 降低使用门槛 → 一键登录、自动填充

### 功能需求映射
- **FR-AUTH-001**: 多种登录方式
  - 一键登录界面 → `QuickLoginPage`
  - 密码登录界面 → `LoginPage`  
  - 生物识别登录 → `BiometricLoginWidget`

- **FR-AUTH-002**: 设备管理
  - 设备列表界面 → `DeviceListPage`
  - 设备管理操作 → `DeviceManagementController`

- **FR-AUTH-003**: 令牌管理
  - 自动令牌刷新 → `TokenInterceptor`
  - 登录状态管理 → `AuthController`

### 验收标准映射
- **AC-AUTH-001**: 登录响应时间<2秒 → 本地缓存、预加载
- **AC-AUTH-002**: 界面友好性 → Material Design 3、动画效果
- **AC-AUTH-003**: 离线体验 → 本地状态保持、网络状态提示

## 🏗️ 技术架构

### Clean Architecture分层
```
lib/features/auth/
├── data/                    # 数据层
│   ├── datasources/        # 数据源
│   │   ├── auth_local_datasource.dart    # 本地数据源
│   │   └── auth_remote_datasource.dart   # 远程数据源
│   ├── models/             # 数据模型
│   │   ├── user_model.dart              # 用户数据模型
│   │   ├── device_model.dart            # 设备数据模型
│   │   └── auth_response_model.dart     # 认证响应模型
│   └── repositories/       # 仓储实现
│       └── auth_repository_impl.dart    # 认证仓储实现
├── domain/                 # 领域层
│   ├── entities/          # 实体
│   │   ├── user_entity.dart            # 用户实体
│   │   └── device_entity.dart          # 设备实体
│   ├── repositories/      # 仓储接口
│   │   └── auth_repository.dart        # 认证仓储接口
│   └── usecases/         # 用例
│       ├── quick_login_usecase.dart    # 一键登录用例
│       ├── password_login_usecase.dart # 密码登录用例
│       ├── get_devices_usecase.dart    # 获取设备列表用例
│       └── logout_usecase.dart         # 登出用例
└── presentation/          # 表现层
    ├── controllers/       # 控制器
    │   ├── auth_controller.dart        # 认证控制器
    │   └── device_controller.dart      # 设备管理控制器
    ├── pages/            # 页面
    │   ├── quick_login_page.dart       # 一键登录页面
    │   ├── login_page.dart             # 密码登录页面
    │   ├── device_list_page.dart       # 设备列表页面
    │   └── profile_page.dart           # 个人资料页面
    └── widgets/          # 组件
        ├── login_form_widget.dart      # 登录表单组件
        ├── biometric_button_widget.dart # 生物识别按钮
        ├── device_item_widget.dart     # 设备列表项
        └── auth_status_widget.dart     # 认证状态组件
```

## 📊 数据层实现

### 本地数据源实现
```dart
// data/datasources/auth_local_datasource.dart
import 'package:sqflite/sqflite.dart';
import 'package:local_auth/local_auth.dart';
import 'package:crypto/crypto.dart';

abstract class AuthLocalDataSource {
  Future<UserModel?> getCachedUser();
  Future<void> cacheUser(UserModel user);
  Future<String?> getStoredToken();
  Future<void> storeToken(String accessToken, String refreshToken);
  Future<void> clearAuthData();
  Future<bool> isBiometricAvailable();
  Future<bool> authenticateWithBiometric();
}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final Database database;
  final LocalAuthentication localAuth;
  
  AuthLocalDataSourceImpl({
    required this.database,
    required this.localAuth,
  });
  
  @override
  Future<UserModel?> getCachedUser() async {
    try {
      final List<Map<String, dynamic>> maps = await database.query(
        'users',
        limit: 1,
      );
      
      if (maps.isNotEmpty) {
        return UserModel.fromJson(maps.first);
      }
      return null;
    } catch (e) {
      throw LocalDataException('Failed to get cached user: $e');
    }
  }
  
  @override
  Future<void> cacheUser(UserModel user) async {
    try {
      await database.insert(
        'users',
        user.toJson(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw LocalDataException('Failed to cache user: $e');
    }
  }
  
  @override
  Future<String?> getStoredToken() async {
    try {
      final List<Map<String, dynamic>> maps = await database.query(
        'auth_tokens',
        columns: ['access_token'],
        limit: 1,
      );
      
      if (maps.isNotEmpty) {
        return maps.first['access_token'] as String?;
      }
      return null;
    } catch (e) {
      throw LocalDataException('Failed to get stored token: $e');
    }
  }
  
  @override
  Future<void> storeToken(String accessToken, String refreshToken) async {
    try {
      await database.insert(
        'auth_tokens',
        {
          'access_token': accessToken,
          'refresh_token': refreshToken,
          'created_at': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw LocalDataException('Failed to store token: $e');
    }
  }
  
  @override
  Future<void> clearAuthData() async {
    try {
      await database.transaction((txn) async {
        await txn.delete('users');
        await txn.delete('auth_tokens');
        await txn.delete('user_configs');
      });
    } catch (e) {
      throw LocalDataException('Failed to clear auth data: $e');
    }
  }
  
  @override
  Future<bool> isBiometricAvailable() async {
    try {
      final bool isAvailable = await localAuth.canCheckBiometrics;
      final bool isDeviceSupported = await localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      return false;
    }
  }
  
  @override
  Future<bool> authenticateWithBiometric() async {
    try {
      final bool didAuthenticate = await localAuth.authenticate(
        localizedReason: '请使用生物识别验证身份',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );
      return didAuthenticate;
    } catch (e) {
      throw BiometricException('Biometric authentication failed: $e');
    }
  }
}
```

### 远程数据源实现
```dart
// data/datasources/auth_remote_datasource.dart
import 'package:dio/dio.dart';

abstract class AuthRemoteDataSource {
  Future<AuthResponseModel> quickLogin(String mobile, String captcha, Map<String, dynamic> deviceInfo);
  Future<AuthResponseModel> passwordLogin(String username, String password, Map<String, dynamic> deviceInfo);
  Future<AuthResponseModel> refreshToken(String refreshToken);
  Future<void> logout();
  Future<List<DeviceModel>> getDevices();
  Future<void> removeDevice(String deviceId);
  Future<void> sendCaptcha(String mobile, String type);
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final Dio dio;
  
  AuthRemoteDataSourceImpl({required this.dio});
  
  @override
  Future<AuthResponseModel> quickLogin(
    String mobile, 
    String captcha, 
    Map<String, dynamic> deviceInfo
  ) async {
    try {
      final response = await dio.post(
        '/v1/auth/quick_login',
        data: {
          'mobile': mobile,
          'captcha': captcha,
          'device_info': deviceInfo,
        },
      );
      
      if (response.data['success'] == true) {
        return AuthResponseModel.fromJson(response.data['data']);
      } else {
        throw ServerException(response.data['message'] ?? 'Login failed');
      }
    } on DioException catch (e) {
      throw ServerException(_handleDioError(e));
    }
  }
  
  @override
  Future<AuthResponseModel> passwordLogin(
    String username, 
    String password, 
    Map<String, dynamic> deviceInfo
  ) async {
    try {
      final response = await dio.post(
        '/v1/auth/login',
        data: {
          'username': username,
          'password': password,
          'device_info': deviceInfo,
        },
      );
      
      if (response.data['success'] == true) {
        return AuthResponseModel.fromJson(response.data['data']);
      } else {
        throw ServerException(response.data['message'] ?? 'Login failed');
      }
    } on DioException catch (e) {
      throw ServerException(_handleDioError(e));
    }
  }
  
  @override
  Future<void> sendCaptcha(String mobile, String type) async {
    try {
      final response = await dio.post(
        '/v1/auth/get_captcha',
        data: {
          'mobile': mobile,
          'type': type,
        },
      );
      
      if (response.data['success'] != true) {
        throw ServerException(response.data['message'] ?? 'Send captcha failed');
      }
    } on DioException catch (e) {
      throw ServerException(_handleDioError(e));
    }
  }
  
  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return '连接超时，请检查网络';
      case DioExceptionType.receiveTimeout:
        return '响应超时，请重试';
      case DioExceptionType.badResponse:
        return e.response?.data['message'] ?? '服务器错误';
      case DioExceptionType.connectionError:
        return '网络连接失败';
      default:
        return '未知错误';
    }
  }
}
```

## 🎮 控制器实现

### 认证控制器
```dart
// presentation/controllers/auth_controller.dart
import 'package:get/get.dart';

class AuthController extends GetxController {
  // 依赖注入
  final QuickLoginUseCase _quickLoginUseCase;
  final PasswordLoginUseCase _passwordLoginUseCase;
  final LogoutUseCase _logoutUseCase;
  final GetCurrentUserUseCase _getCurrentUserUseCase;
  
  AuthController({
    required QuickLoginUseCase quickLoginUseCase,
    required PasswordLoginUseCase passwordLoginUseCase,
    required LogoutUseCase logoutUseCase,
    required GetCurrentUserUseCase getCurrentUserUseCase,
  }) : _quickLoginUseCase = quickLoginUseCase,
       _passwordLoginUseCase = passwordLoginUseCase,
       _logoutUseCase = logoutUseCase,
       _getCurrentUserUseCase = getCurrentUserUseCase;
  
  // 响应式状态
  final _isLoading = false.obs;
  final _user = Rxn<UserEntity>();
  final _authStatus = AuthStatus.initial.obs;
  final _errorMessage = ''.obs;
  
  // Getters
  bool get isLoading => _isLoading.value;
  UserEntity? get user => _user.value;
  AuthStatus get authStatus => _authStatus.value;
  String get errorMessage => _errorMessage.value;
  bool get isAuthenticated => _authStatus.value == AuthStatus.authenticated;
  
  @override
  void onInit() {
    super.onInit();
    _checkAuthStatus();
    _setupTokenRefresh();
  }
  
  /// 一键登录
  Future<void> quickLogin(String mobile, String captcha) async {
    _setLoading(true);
    _clearError();
    
    try {
      final result = await _quickLoginUseCase(
        QuickLoginParams(mobile: mobile, captcha: captcha)
      );
      
      result.fold(
        (failure) => _handleFailure(failure),
        (authResponse) => _handleAuthSuccess(authResponse),
      );
    } finally {
      _setLoading(false);
    }
  }
  
  /// 密码登录
  Future<void> passwordLogin(String username, String password) async {
    _setLoading(true);
    _clearError();
    
    try {
      final result = await _passwordLoginUseCase(
        PasswordLoginParams(username: username, password: password)
      );
      
      result.fold(
        (failure) => _handleFailure(failure),
        (authResponse) => _handleAuthSuccess(authResponse),
      );
    } finally {
      _setLoading(false);
    }
  }
  
  /// 生物识别登录
  Future<void> biometricLogin() async {
    try {
      _setLoading(true);
      
      // 检查生物识别可用性
      final isAvailable = await _checkBiometricAvailability();
      if (!isAvailable) {
        _setError('设备不支持生物识别或未设置');
        return;
      }
      
      // 执行生物识别
      final isAuthenticated = await _authenticateWithBiometric();
      if (!isAuthenticated) {
        _setError('生物识别验证失败');
        return;
      }
      
      // 使用存储的令牌登录
      await _loginWithStoredToken();
      
    } catch (e) {
      _setError('生物识别登录失败: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// 登出
  Future<void> logout() async {
    try {
      _setLoading(true);
      
      final result = await _logoutUseCase(NoParams());
      result.fold(
        (failure) => _handleFailure(failure),
        (_) => _handleLogoutSuccess(),
      );
    } finally {
      _setLoading(false);
    }
  }
  
  /// 检查认证状态
  Future<void> _checkAuthStatus() async {
    try {
      final result = await _getCurrentUserUseCase(NoParams());
      result.fold(
        (failure) => _authStatus.value = AuthStatus.unauthenticated,
        (user) {
          _user.value = user;
          _authStatus.value = AuthStatus.authenticated;
        },
      );
    } catch (e) {
      _authStatus.value = AuthStatus.unauthenticated;
    }
  }
  
  /// 设置令牌自动刷新
  void _setupTokenRefresh() {
    // 使用定时器定期检查令牌状态
    Timer.periodic(const Duration(minutes: 10), (timer) {
      if (isAuthenticated) {
        _refreshTokenIfNeeded();
      } else {
        timer.cancel();
      }
    });
  }
  
  void _handleAuthSuccess(AuthResponseEntity authResponse) {
    _user.value = authResponse.user;
    _authStatus.value = AuthStatus.authenticated;
    _clearError();
    
    // 导航到主页
    Get.offAllNamed(Routes.HOME);
    
    // 显示成功提示
    Get.snackbar(
      '登录成功',
      '欢迎回来，${authResponse.user.username ?? authResponse.user.mobile}',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }
  
  void _handleFailure(Failure failure) {
    _authStatus.value = AuthStatus.unauthenticated;
    _setError(failure.message);
    
    Get.snackbar(
      '登录失败',
      failure.message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }
  
  void _handleLogoutSuccess() {
    _user.value = null;
    _authStatus.value = AuthStatus.unauthenticated;
    _clearError();
    
    // 导航到登录页
    Get.offAllNamed(Routes.LOGIN);
    
    Get.snackbar(
      '已登出',
      '您已成功登出',
      snackPosition: SnackPosition.TOP,
    );
  }
  
  void _setLoading(bool loading) => _isLoading.value = loading;
  void _setError(String error) => _errorMessage.value = error;
  void _clearError() => _errorMessage.value = '';
}

enum AuthStatus {
  initial,
  authenticated,
  unauthenticated,
}
```

## 🎨 UI页面实现

### 一键登录页面
```dart
// presentation/pages/quick_login_page.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class QuickLoginPage extends GetView<AuthController> {
  const QuickLoginPage({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('一键登录'),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 40),
              
              // Logo和标题
              _buildHeader(),
              
              const SizedBox(height: 60),
              
              // 登录表单
              Expanded(
                child: _buildLoginForm(),
              ),
              
              // 其他登录方式
              _buildAlternativeLogin(),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildHeader() {
    return Column(
      children: [
        // Logo
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Theme.of(Get.context!).primaryColor,
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Icon(
            Icons.school,
            size: 40,
            color: Colors.white,
          ),
        ),
        
        const SizedBox(height: 20),
        
        // 标题
        Text(
          'CheeStack',
          style: Theme.of(Get.context!).textTheme.headlineMedium?.copyWith(
            
          ),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          '智能学习，科学记忆',
          style: Theme.of(Get.context!).textTheme.bodyLarge?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
  
  Widget _buildLoginForm() {
    return GetBuilder<AuthController>(
      builder: (controller) {
        return QuickLoginFormWidget(
          onLogin: controller.quickLogin,
          isLoading: controller.isLoading,
        );
      },
    );
  }
  
  Widget _buildAlternativeLogin() {
    return Column(
      children: [
        // 分割线
        Row(
          children: [
            const Expanded(child: Divider()),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '其他登录方式',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            const Expanded(child: Divider()),
          ],
        ),
        
        const SizedBox(height: 20),
        
        // 登录方式按钮
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // 密码登录
            _buildAlternativeButton(
              icon: Icons.password,
              label: '密码登录',
              onTap: () => Get.toNamed(Routes.PASSWORD_LOGIN),
            ),
            
            // 生物识别登录
            BiometricButtonWidget(
              onAuthenticated: controller.biometricLogin,
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildAlternativeButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, size: 24),
            const SizedBox(height: 4),
            Text(label, style: const TextStyle(fontSize: 12)),
          ],
        ),
      ),
    );
  }
}
```

---

**注意**：本前端实现指导基于Clean Architecture原则和GetX状态管理，与现有后端API完美集成。所有实现都严格对应具体的功能需求和验收标准，确保代码质量和用户体验。
