# 创作模块后端实现指导

## 📋 概述

本文档详细说明创作模块FastAPI后端的数据模型、API接口、业务逻辑和数据同步机制。后端采用分层架构设计，支持高并发访问和数据一致性保证。

## 🏗️ 架构设计

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
├─────────────────────────────────────────────────────────────┤
│  API Routes     │  Request/Response │  Middleware           │
│  • /books       │  • Pydantic       │  • Authentication     │
│  • /cards       │  • Validation     │  • Rate Limiting      │
│  • /sync        │  • Serialization  │  • CORS               │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
├─────────────────────────────────────────────────────────────┤
│  Services       │  Use Cases        │  Business Logic       │
│  • BookService  │  • CreateBook     │  • Validation         │
│  • CardService  │  • UpdateCard     │  • Authorization      │
│  • SyncService  │  • SyncData       │  • Conflict Resolution│
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
├─────────────────────────────────────────────────────────────┤
│  Models         │  Entities         │  Value Objects        │
│  • Book         │  • User           │  • PrivacyType        │
│  • Card         │  • BookCard       │  • CardType           │
│  • CardAsset    │  • SyncRecord     │  • SyncStatus         │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  Infrastructure Layer                       │
├─────────────────────────────────────────────────────────────┤
│  Database       │  External APIs    │  Storage              │
│  • PostgreSQL   │  • File Upload    │  • Redis Cache        │
│  • Tortoise ORM │  • CDN Service    │  • Object Storage     │
│  • Migrations   │  • AI Services    │  • Backup Service     │
└─────────────────────────────────────────────────────────────┘
```

## 📊 数据模型设计

### 1. Book模型 (书籍)

```python
from tortoise.models import Model
from tortoise import fields
from apps.general.models import TimeStampModelMixin, UrlCdnField
from apps.general.enums import PrivacyType

class Book(TimeStampModelMixin):
    """书籍模型"""
    
    # 基础字段
    user = fields.ForeignKeyField(
        "models.User",
        related_name="books",
        description="创建者"
    )
    name = fields.CharField(
        max_length=100,
        description="书籍名称"
    )
    brief = fields.TextField(
        null=True,
        description="书籍简介"
    )
    cover = UrlCdnField(
        max_length=512,
        null=True,
        description="封面图片URL"
    )
    privacy = fields.CharEnumField(
        enum_type=PrivacyType,
        default=PrivacyType.FREE,
        description="隐私设置"
    )
    
    # 统计字段
    card_count = fields.IntField(
        default=0,
        description="卡片数量"
    )
    view_count = fields.IntField(
        default=0,
        description="查看次数"
    )
    
    # 关联关系
    cards = fields.ManyToManyField(
        "models.Card",
        through="book_card",
        related_name="books",
        description="关联的卡片"
    )
    
    # 同步字段
    is_dirty = fields.BooleanField(
        default=False,
        description="是否有未同步的更改"
    )
    synced_at = fields.DatetimeField(
        null=True,
        description="最后同步时间"
    )
    
    class Meta:
        table = "books"
        indexes = [
            ("user", "created_at"),
            ("user", "name"),
            ("privacy",),
            ("is_dirty",),
        ]
    
    def __str__(self):
        return f"Book({self.name})"
```

### 2. Card模型 (卡片)

```python
class Card(TimeStampModelMixin):
    """卡片模型"""
    
    # 基础字段
    user = fields.ForeignKeyField(
        "models.User",
        related_name="cards",
        description="创建者"
    )
    type = fields.CharField(
        max_length=50,
        default="basic",
        description="卡片类型"
    )
    type_version = fields.IntField(
        default=1,
        description="类型版本"
    )
    title = fields.CharField(
        max_length=200,
        null=True,
        description="卡片标题"
    )
    question = fields.TextField(
        null=True,
        description="问题内容"
    )
    answer = fields.TextField(
        null=True,
        description="答案内容"
    )
    extra = fields.JSONField(
        default=dict,
        description="扩展数据"
    )
    
    # 学习相关
    schedule_id = fields.IntField(
        null=True,
        description="学习计划ID"
    )
    difficulty = fields.FloatField(
        default=0.0,
        description="难度系数"
    )
    
    # 关联关系
    books = fields.ManyToManyField(
        "models.Book",
        through="book_card",
        related_name="cards",
        description="所属书籍"
    )
    
    # 同步字段
    is_dirty = fields.BooleanField(
        default=False,
        description="是否有未同步的更改"
    )
    synced_at = fields.DatetimeField(
        null=True,
        description="最后同步时间"
    )
    
    class Meta:
        table = "cards"
        indexes = [
            ("user", "created_at"),
            ("user", "type"),
            ("title",),
            ("is_dirty",),
        ]
```

### 3. CardAsset模型 (卡片资源)

```python
class CardAsset(TimeStampModelMixin):
    """卡片资源模型"""
    
    card = fields.ForeignKeyField(
        "models.Card",
        related_name="card_assets",
        description="所属卡片"
    )
    asset_id = fields.CharField(
        max_length=100,
        description="资源ID"
    )
    type = fields.CharField(
        max_length=50,
        description="资源类型"
    )
    name = fields.CharField(
        max_length=200,
        null=True,
        description="资源名称"
    )
    url = fields.CharField(
        max_length=512,
        null=True,
        description="资源URL"
    )
    text = fields.TextField(
        null=True,
        description="文本内容"
    )
    is_correct = fields.BooleanField(
        default=False,
        description="是否为正确答案"
    )
    
    class Meta:
        table = "card_assets"
        indexes = [
            ("card", "type"),
            ("asset_id",),
        ]
```

## 🔌 API接口设计

### 1. 书籍管理API

```python
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from apps.auth.dependencies import get_current_user
from apps.study.schemas import BookCreate, BookUpdate, BookResponse
from apps.study.services import BookService

router = APIRouter(prefix="/books", tags=["books"])

@router.get("/", response_model=List[BookResponse])
async def get_books(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    privacy: Optional[str] = Query(None),
    order_by: str = Query("-created_at"),
    current_user = Depends(get_current_user)
):
    """获取用户书籍列表"""
    return await BookService.get_user_books(
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        search=search,
        privacy=privacy,
        order_by=order_by
    )

@router.post("/", response_model=BookResponse)
async def create_book(
    book_data: BookCreate,
    current_user = Depends(get_current_user)
):
    """创建新书籍"""
    return await BookService.create_book(
        user_id=current_user.id,
        book_data=book_data
    )

@router.get("/{book_id}", response_model=BookResponse)
async def get_book(
    book_id: int,
    current_user = Depends(get_current_user)
):
    """获取书籍详情"""
    book = await BookService.get_book_by_id(book_id)
    if not book or book.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="Book not found")
    return book

@router.put("/{book_id}", response_model=BookResponse)
async def update_book(
    book_id: int,
    book_data: BookUpdate,
    current_user = Depends(get_current_user)
):
    """更新书籍"""
    return await BookService.update_book(
        book_id=book_id,
        user_id=current_user.id,
        book_data=book_data
    )

@router.delete("/{book_id}")
async def delete_book(
    book_id: int,
    current_user = Depends(get_current_user)
):
    """删除书籍"""
    await BookService.delete_book(
        book_id=book_id,
        user_id=current_user.id
    )
    return {"message": "Book deleted successfully"}
```

### 2. 卡片管理API

```python
@router.get("/cards/", response_model=List[CardResponse])
async def get_cards(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    book_id: Optional[int] = Query(None),
    search: Optional[str] = Query(None),
    card_type: Optional[str] = Query(None),
    current_user = Depends(get_current_user)
):
    """获取用户卡片列表"""
    return await CardService.get_user_cards(
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        book_id=book_id,
        search=search,
        card_type=card_type
    )

@router.post("/cards/", response_model=CardResponse)
async def create_card(
    card_data: CardCreate,
    current_user = Depends(get_current_user)
):
    """创建新卡片"""
    return await CardService.create_card(
        user_id=current_user.id,
        card_data=card_data
    )
```

## 🔄 业务服务层

### 1. BookService (书籍服务)

```python
from typing import List, Optional
from tortoise.expressions import Q
from apps.study.models import Book
from apps.study.schemas import BookCreate, BookUpdate

class BookService:
    """书籍业务服务"""
    
    @staticmethod
    async def get_user_books(
        user_id: int,
        skip: int = 0,
        limit: int = 20,
        search: Optional[str] = None,
        privacy: Optional[str] = None,
        order_by: str = "-created_at"
    ) -> List[Book]:
        """获取用户书籍列表"""
        
        query = Book.filter(user_id=user_id)
        
        # 搜索过滤
        if search:
            query = query.filter(
                Q(name__icontains=search) | 
                Q(brief__icontains=search)
            )
        
        # 隐私过滤
        if privacy:
            query = query.filter(privacy=privacy)
        
        # 排序
        if order_by.startswith('-'):
            query = query.order_by(f"-{order_by[1:]}")
        else:
            query = query.order_by(order_by)
        
        return await query.offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_book(
        user_id: int,
        book_data: BookCreate
    ) -> Book:
        """创建新书籍"""
        # 数据验证
        if await Book.filter(user_id=user_id, name=book_data.name).exists():
            raise ValueError("Book name already exists")
        
        # 创建书籍
        book = await Book.create(
            user_id=user_id,
            name=book_data.name,
            brief=book_data.brief,
            cover=book_data.cover,
            privacy=book_data.privacy,
            is_dirty=True  # 标记为需要同步
        )
        
        # 触发同步任务
        await SyncService.schedule_book_sync(book.id)
        
        return book
    
    @staticmethod
    async def update_book(
        book_id: int,
        user_id: int,
        book_data: BookUpdate
    ) -> Book:
        """更新书籍"""
        
        book = await Book.get_or_none(id=book_id, user_id=user_id)
        if not book:
            raise ValueError("Book not found")
        
        # 更新字段
        update_data = book_data.dict(exclude_unset=True)
        update_data['is_dirty'] = True  # 标记为需要同步
        
        await book.update_from_dict(update_data)
        await book.save()
        
        # 触发同步任务
        await SyncService.schedule_book_sync(book.id)
        
        return book
```

### 2. SyncService (同步服务)

```python
import asyncio
from typing import Dict, Any
from apps.study.models import Book, Card, SyncRecord
from apps.general.enums import SyncStatus

class SyncService:
    """数据同步服务"""
    
    @staticmethod
    async def sync_user_data(user_id: int) -> Dict[str, Any]:
        """同步用户所有数据"""
        
        results = {
            "books": {"success": 0, "failed": 0},
            "cards": {"success": 0, "failed": 0},
            "errors": []
        }
        
        try:
            # 同步书籍
            books_result = await SyncService._sync_user_books(user_id)
            results["books"] = books_result
            
            # 同步卡片
            cards_result = await SyncService._sync_user_cards(user_id)
            results["cards"] = cards_result
            
        except Exception as e:
            results["errors"].append(str(e))
        
        return results
    
    @staticmethod
    async def _sync_user_books(user_id: int) -> Dict[str, int]:
        """同步用户书籍"""
        
        # 获取需要同步的书籍
        dirty_books = await Book.filter(
            user_id=user_id,
            is_dirty=True
        ).all()
        
        success_count = 0
        failed_count = 0
        
        for book in dirty_books:
            try:
                # 执行同步逻辑
                await SyncService._sync_book_to_external(book)
                
                # 更新同步状态
                book.is_dirty = False
                book.synced_at = datetime.utcnow()
                await book.save()
                
                success_count += 1
                
            except Exception as e:
                failed_count += 1
                # 记录同步失败
                await SyncRecord.create(
                    user_id=user_id,
                    entity_type="book",
                    entity_id=book.id,
                    status=SyncStatus.FAILED,
                    error_message=str(e)
                )
        
        return {"success": success_count, "failed": failed_count}
```

## 🔐 安全和权限控制

### 1. 数据访问权限

```python
from fastapi import HTTPException
from apps.auth.models import User

async def check_book_permission(book_id: int, user: User, action: str = "read"):
    """检查书籍访问权限"""
    
    book = await Book.get_or_none(id=book_id)
    if not book:
        raise HTTPException(status_code=404, detail="Book not found")
    
    # 所有者权限
    if book.user_id == user.id:
        return True
    
    # 公开书籍的读权限
    if action == "read" and book.privacy == PrivacyType.FREE:
        return True
    
    # VIP用户的读权限
    if action == "read" and book.privacy == PrivacyType.VIP and user.is_vip:
        return True
    
    raise HTTPException(status_code=403, detail="Permission denied")
```

### 2. 数据验证

```python
from pydantic import BaseModel, validator
from typing import Optional

class BookCreate(BaseModel):
    name: str
    brief: Optional[str] = None
    cover: Optional[str] = None
    privacy: str = "free"
    
    @validator('name')
    def validate_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Book name cannot be empty')
        if len(v) > 100:
            raise ValueError('Book name too long')
        return v.strip()
    
    @validator('privacy')
    def validate_privacy(cls, v):
        if v not in ['free', 'private', 'vip']:
            raise ValueError('Invalid privacy setting')
        return v
```

## 📈 性能优化

### 1. 数据库优化

```python
# 使用索引优化查询
class Book(TimeStampModelMixin):
    class Meta:
        indexes = [
            ("user", "created_at"),  # 用户书籍列表查询
            ("user", "name"),        # 用户书籍搜索
            ("privacy",),            # 公开书籍查询
            ("is_dirty",),           # 同步状态查询
        ]

# 使用预加载优化关联查询
books = await Book.filter(user_id=user_id).prefetch_related('cards').all()
```

### 2. 缓存策略

```python
import redis
from functools import wraps

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expire_time: int = 300):
    """结果缓存装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            redis_client.setex(
                cache_key,
                expire_time,
                json.dumps(result, default=str)
            )
            
            return result
        return wrapper
    return decorator
```

## 🧪 测试策略

### 1. 单元测试

```python
import pytest
from apps.study.services import BookService
from apps.study.schemas import BookCreate

@pytest.mark.asyncio
async def test_create_book():
    """测试创建书籍"""
    book_data = BookCreate(
        name="Test Book",
        brief="Test Brief",
        privacy="free"
    )
    
    book = await BookService.create_book(
        user_id=1,
        book_data=book_data
    )
    
    assert book.name == "Test Book"
    assert book.brief == "Test Brief"
    assert book.privacy == "free"
    assert book.is_dirty is True
```

### 2. 集成测试

```python
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

def test_get_books_api():
    """测试获取书籍列表API"""
    response = client.get(
        "/v1/books/",
        headers={"Authorization": "Bearer test_token"}
    )
    
    assert response.status_code == 200
    assert isinstance(response.json(), list)
```

---

**注意**：本文档描述的是创作模块后端实现的核心架构和关键组件。具体实现时应根据实际需求进行调整和优化，特别是在性能、安全性和可扩展性方面。
