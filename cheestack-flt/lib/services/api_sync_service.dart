part of services;

/// API数据同步服务
/// 负责从后端API获取数据并同步到本地数据库
class ApiSyncService extends GetxService {
  static ApiSyncService get to => Get.find();

  late final DaoManager _daoManager;

  // 同步状态
  final RxBool _isSyncing = false.obs;
  final RxString _syncStatus = 'idle'.obs;
  final RxDouble _syncProgress = 0.0.obs;

  // Getters
  bool get isSyncing => _isSyncing.value;
  String get syncStatus => _syncStatus.value;
  double get syncProgress => _syncProgress.value;

  Future<ApiSyncService> init() async {
    // 等待依赖服务初始化完成
    while (!Get.isRegistered<DaoManager>()) {
      await Future.delayed(const Duration(milliseconds: 10));
    }

    _daoManager = DaoManager.to;
    return this;
  }

  /// 全量同步所有数据
  Future<bool> syncAllData({String? userId}) async {
    if (_isSyncing.value) {
      Console.log('Sync already in progress');
      return false;
    }

    _isSyncing.value = true;
    _syncStatus.value = 'syncing';
    _syncProgress.value = 0.0;

    try {
      final currentUserId = userId ?? await _getCurrentUserId();
      if (currentUserId == null) {
        Console.log('No user logged in, skipping sync');
        return false;
      }

      Console.log('Starting full data sync for user: $currentUserId');

      // 1. 同步用户数据 (10%)
      await _syncUserData(currentUserId);
      _syncProgress.value = 0.1;

      // 2. 同步书籍数据 (30%)
      await _syncBooksData(currentUserId);
      _syncProgress.value = 0.3;

      // 3. 同步卡片数据 (60%)
      await _syncCardsData(currentUserId);
      _syncProgress.value = 0.6;

      // 4. 同步资源数据 (80%)
      await _syncAssetsData(currentUserId);
      _syncProgress.value = 0.8;

      // 5. 同步学习记录 (90%)
      await _syncStudyRecords(currentUserId);
      _syncProgress.value = 0.9;

      // 6. 同步书籍计划 (100%)
      await _syncBookSchedules(currentUserId);
      _syncProgress.value = 1.0;

      _syncStatus.value = 'success';
      Console.log('Full data sync completed successfully');
      return true;
    } catch (e) {
      _syncStatus.value = 'failed';
      Console.log('Full data sync failed: $e');
      return false;
    } finally {
      _isSyncing.value = false;
    }
  }

  /// 获取当前用户ID
  Future<String?> _getCurrentUserId() async {
    return StorageService.to.getString('current_user_id');
  }

  /// 同步用户数据
  Future<void> _syncUserData(String userId) async {
    try {
      Console.log('Syncing user data...');

      // 调用用户API获取用户信息
      final response = await OxHttp.to.get('/auth/me');
      if (response.statusCode == 200) {
        final userData = response.data['data'];

        // 转换为UserModel并保存到本地数据库
        final user = UserModel(
          id: userData['id'],
          username: userData['username'],
          mobile: userData['mobile'],
          email: userData['email'],
          avatar: userData['avatar'],
          intro: userData['intro'],
          createdAt: userData['created_at'] != null
              ? DateTime.parse(userData['created_at'])
              : null,
          updatedAt: userData['updated_at'] != null
              ? DateTime.parse(userData['updated_at'])
              : null,
        );

        // 检查用户是否已存在
        final existingUser = await _daoManager.userDao.findById(user.id!);
        if (existingUser != null) {
          await _daoManager.userDao.update(user);
        } else {
          await _daoManager.userDao.insert(user);
        }

        // 同步用户配置
        if (userData['config'] != null) {
          final configData = userData['config'];
          final config = ConfigModel(
            isAutoPlayAudio: configData['is_auto_play_audio'] ?? false,
            isAutoPlayAiAudio: configData['is_auto_play_ai_audio'] ?? false,
            reviewNumber: configData['review_number'] ?? 20,
            studyNumber: configData['study_number'] ?? 20,
            studyType: configData['study_type'] ?? 1,
            currentStudyId: configData['current_study_id'],
            editingBookId: configData['editing_book_id'],
            createdAt: configData['created_at'] != null
                ? DateTime.parse(configData['created_at'])
                : null,
            updatedAt: configData['updated_at'] != null
                ? DateTime.parse(configData['updated_at'])
                : null,
          );

          await _daoManager.userConfigDao.insertOrUpdate(userId, config);
        }

        Console.log('User data synced successfully');
      }
    } catch (e) {
      Console.log('Failed to sync user data: $e');
      rethrow;
    }
  }

  /// 同步书籍数据
  Future<void> _syncBooksData(String userId) async {
    try {
      Console.log('Syncing books data...');
      Console.log('User ID: $userId');

      // 验证userId不为空
      if (userId.isEmpty) {
        throw Exception('User ID is empty');
      }

      int skip = 0;
      const int limit = 50;
      bool hasMore = true;

      while (hasMore) {
        final response = await OxHttp.to.get(
          '/v1/books',
          queryParameters: {
            'skip': skip,
            'limit': limit,
            'order': '-created_at',
          },
        );

        if (response.statusCode == 200) {
          final List<dynamic> booksData = response.data['data'] ?? [];

          if (booksData.isEmpty) {
            hasMore = false;
            break;
          }

          for (final bookData in booksData) {
            try {
              // 验证必要字段
              if (bookData['id'] == null) {
                Console.log('Skipping book with null id: $bookData');
                continue;
              }

              final book = BookModel(
                id: bookData['id'],
                name: bookData['name'] ?? '',
                brief: bookData['brief'] ?? '',
                cover: bookData['cover'] ?? '',
                privacy: bookData['privacy'] ?? 'free',
                createdAt:
                    bookData['created_at'] ?? DateTime.now().toIso8601String(),
                updatedAt:
                    bookData['updated_at'] ?? DateTime.now().toIso8601String(),
              );

              Console.log('Processing book: ${book.name} (ID: ${book.id})');

              // 检查书籍是否已存在
              final existingBook = await _daoManager.bookDao.findById(book.id!);
              if (existingBook != null) {
                Console.log('Updating existing book: ${book.id}');
                await _daoManager.bookDao.update(book);
              } else {
                Console.log('Inserting new book: ${book.id} for user: $userId');
                await _daoManager.bookDao.insertWithUserId(book, userId);
              }
            } catch (e) {
              Console.log('Failed to process book ${bookData['id']}: $e');
              // 继续处理下一本书，不中断整个同步过程
            }
          }

          skip += limit;
          if (booksData.length < limit) {
            hasMore = false;
          }
        } else {
          hasMore = false;
        }
      }

      Console.log('Books data synced successfully');
    } catch (e) {
      Console.log('Failed to sync books data: $e');
      rethrow;
    }
  }

  /// 同步卡片数据
  Future<void> _syncCardsData(String userId) async {
    try {
      Console.log('Syncing cards data...');

      // 获取所有书籍
      final books = await _daoManager.bookDao.findByUserId(userId);

      for (final book in books) {
        int skip = 0;
        const int limit = 50;
        bool hasMore = true;

        while (hasMore) {
          final response = await OxHttp.to.get(
            '/v1/cards',
            queryParameters: {
              'skip': skip,
              'limit': limit,
              'filters': 'books__id:${book.id}',
              'order': '-created_at',
            },
          );

          if (response.statusCode == 200) {
            final List<dynamic> cardsData = response.data['data'] ?? [];

            if (cardsData.isEmpty) {
              hasMore = false;
              break;
            }

            for (final cardData in cardsData) {
              try {
                // 验证必要字段
                if (cardData['id'] == null) {
                  Console.log('Skipping card with null id: $cardData');
                  continue;
                }

                final card = CardModel(
                  id: cardData['id'],
                  type: cardData['type'] ?? 'general',
                  typeVersion: cardData['type_version'] ?? 1,
                  title: cardData['title'] ?? '',
                  question: cardData['question'] ?? '',
                  answer: cardData['answer'] ?? '',
                  extra: cardData['extra'],
                  scheduleId: cardData['schedule_id'],
                  createdAt: cardData['created_at'] != null
                      ? DateTime.parse(cardData['created_at'])
                      : null,
                  updatedAt: cardData['updated_at'] != null
                      ? DateTime.parse(cardData['updated_at'])
                      : null,
                );

                Console.log('Processing card: ${card.title} (ID: ${card.id})');

                // 检查卡片是否已存在
                final existingCard =
                    await _daoManager.cardDao.findById(card.id!);
                if (existingCard != null) {
                  Console.log('Updating existing card: ${card.id}');
                  await _daoManager.cardDao.update(card);
                } else {
                  Console.log(
                      'Inserting new card: ${card.id} for book: ${book.id}');
                  await _daoManager.cardDao
                      .insertWithIds(card, userId, book.id!);
                }

                // 同步卡片资源
                if (cardData['card_assets'] != null) {
                  await _syncCardAssets(cardData['card_assets'], card.id!);
                }
              } catch (e) {
                Console.log('Failed to process card ${cardData['id']}: $e');
                // 继续处理下一张卡片，不中断整个同步过程
              }
            }

            skip += limit;
            if (cardsData.length < limit) {
              hasMore = false;
            }
          } else {
            hasMore = false;
          }
        }
      }

      Console.log('Cards data synced successfully');
    } catch (e) {
      Console.log('Failed to sync cards data: $e');
      rethrow;
    }
  }

  /// 同步卡片资源
  Future<void> _syncCardAssets(List<dynamic> assetsData, int cardId) async {
    try {
      // 先删除现有的卡片资源
      await _daoManager.cardAssetDao.deleteByCardId(cardId);

      for (final assetData in assetsData) {
        final asset = CardAsset(
          id: assetData['id'],
          cardId: cardId,
          assetId: assetData['asset_id'],
          isCorrect: assetData['is_correct'] ?? true,
          type: assetData['type'],
          text: assetData['text'],
          url: assetData['url'],
          name: assetData['name'],
        );

        await _daoManager.cardAssetDao.insertCardAsset(asset);
      }
    } catch (e) {
      Console.log('Failed to sync card assets: $e');
      rethrow;
    }
  }

  /// 同步在学习的卡片数据
  Future<void> _syncStudyingCardsData(String userId) async {
    try {
      Console.log('Syncing studying cards data...');

      // 1. 获取需要复习的卡片ID列表
      final reviewCardIds =
          await _daoManager.studyRecordDao.getCardsForReview(userId);

      // 2. 获取新卡片ID列表（有学习记录但还在学习中的）
      final studyingCardIds = await _getStudyingCardIds(userId);

      // 3. 合并去重
      final allStudyingCardIds =
          <int>{...reviewCardIds, ...studyingCardIds}.toList();

      if (allStudyingCardIds.isEmpty) {
        Console.log('No studying cards found');
        return;
      }

      Console.log('Found ${allStudyingCardIds.length} studying cards to sync');

      // 4. 批量同步这些卡片
      await _syncSpecificCards(userId, allStudyingCardIds);

      Console.log('Studying cards data synced successfully');
    } catch (e) {
      Console.log('Failed to sync studying cards data: $e');
      rethrow;
    }
  }

  /// 获取正在学习中的卡片ID列表
  Future<List<int>> _getStudyingCardIds(String userId) async {
    try {
      // 查询有学习记录但还在学习阶段的卡片
      // state: 0=新卡片, 1=学习中, 2=复习中, 3=重新学习
      final result = await _daoManager.studyRecordDao.db.rawQuery('''
        SELECT DISTINCT sr1.card_id
        FROM study_records sr1
        INNER JOIN (
          SELECT card_id, MAX(review_time) as latest_review
          FROM study_records
          WHERE user_id = ?
          GROUP BY card_id
        ) sr2 ON sr1.card_id = sr2.card_id AND sr1.review_time = sr2.latest_review
        WHERE sr1.user_id = ?
          AND sr1.state IN (1, 2, 3)
      ''', [userId, userId]);

      return result.map((row) => row['card_id'] as int).toList();
    } catch (e) {
      Console.log('Failed to get studying card IDs: $e');
      return [];
    }
  }

  /// 同步指定的卡片列表
  Future<void> _syncSpecificCards(String userId, List<int> cardIds) async {
    try {
      // 分批处理，避免URL过长
      const int batchSize = 50;

      for (int i = 0; i < cardIds.length; i += batchSize) {
        final batch = cardIds.skip(i).take(batchSize).toList();
        final cardIdsStr = batch.join(',');

        final response = await OxHttp.to.get(
          '/v1/cards',
          queryParameters: {
            'filters': 'id__in:$cardIdsStr',
            'limit': batchSize,
            'order': '-updated_at',
          },
        );

        if (response.statusCode == 200) {
          final List<dynamic> cardsData = response.data['data'] ?? [];

          for (final cardData in cardsData) {
            final card = CardModel(
              id: cardData['id'],
              type: cardData['type'],
              typeVersion: cardData['type_version'],
              title: cardData['title'],
              question: cardData['question'],
              answer: cardData['answer'],
              extra: cardData['extra'],
              scheduleId: cardData['schedule_id'],
              createdAt: cardData['created_at'] != null
                  ? DateTime.parse(cardData['created_at'])
                  : null,
              updatedAt: cardData['updated_at'] != null
                  ? DateTime.parse(cardData['updated_at'])
                  : null,
            );

            // 更新卡片数据
            final existingCard = await _daoManager.cardDao.findById(card.id!);
            if (existingCard != null) {
              await _daoManager.cardDao.update(card);
            } else {
              // 如果本地没有这张卡片，需要找到它所属的书籍
              final bookId = await _getCardBookId(card.id!, cardData);
              if (bookId != null) {
                await _daoManager.cardDao.insertWithIds(card, userId, bookId);
              }
            }

            // 同步卡片资源
            if (cardData['card_assets'] != null) {
              await _syncCardAssets(cardData['card_assets'], card.id!);
            }
          }
        }
      }
    } catch (e) {
      Console.log('Failed to sync specific cards: $e');
      rethrow;
    }
  }

  /// 同步在学习书籍的所有卡片数据
  Future<void> _syncStudyingBooksCardsData(String userId) async {
    try {
      Console.log('Syncing studying books cards data...');

      // 1. 获取在学习中的书籍ID列表
      final studyingBookIds = await _getStudyingBookIds(userId);

      if (studyingBookIds.isEmpty) {
        Console.log('No studying books found');
        return;
      }

      Console.log(
          'Found ${studyingBookIds.length} studying books to sync cards');

      // 2. 为每个在学习的书籍同步所有卡片
      for (final bookId in studyingBookIds) {
        await _syncBookAllCards(userId, bookId);
      }

      Console.log('Studying books cards data synced successfully');
    } catch (e) {
      Console.log('Failed to sync studying books cards data: $e');
      rethrow;
    }
  }

  /// 获取在学习中的书籍ID列表
  Future<List<int>> _getStudyingBookIds(String userId) async {
    try {
      // 查询有学习记录的书籍ID
      final result = await _daoManager.studyRecordDao.db.rawQuery('''
        SELECT DISTINCT ubc.book_id
        FROM study_records sr
        INNER JOIN user_book_cards ubc ON sr.card_id = ubc.card_id
        WHERE sr.user_id = ?
          AND ubc.user_id = ?
        ORDER BY MAX(sr.review_time) DESC
      ''', [userId, userId]);

      return result.map((row) => row['book_id'] as int).toList();
    } catch (e) {
      Console.log('Failed to get studying book IDs: $e');
      return [];
    }
  }

  /// 同步指定书籍的所有卡片
  Future<void> _syncBookAllCards(String userId, int bookId) async {
    try {
      Console.log('Syncing all cards for book: $bookId');

      int skip = 0;
      const int limit = 50;
      bool hasMore = true;

      while (hasMore) {
        final response = await OxHttp.to.get(
          '/v1/cards',
          queryParameters: {
            'filters': 'books__id:$bookId',
            'skip': skip,
            'limit': limit,
            'order': '-updated_at',
          },
        );

        if (response.statusCode == 200) {
          final List<dynamic> cardsData = response.data['data'] ?? [];

          if (cardsData.isEmpty) {
            hasMore = false;
            break;
          }

          for (final cardData in cardsData) {
            final card = CardModel(
              id: cardData['id'],
              type: cardData['type'],
              typeVersion: cardData['type_version'],
              title: cardData['title'],
              question: cardData['question'],
              answer: cardData['answer'],
              extra: cardData['extra'],
              scheduleId: cardData['schedule_id'],
              createdAt: cardData['created_at'] != null
                  ? DateTime.parse(cardData['created_at'])
                  : null,
              updatedAt: cardData['updated_at'] != null
                  ? DateTime.parse(cardData['updated_at'])
                  : null,
            );

            // 更新或插入卡片数据
            final existingCard = await _daoManager.cardDao.findById(card.id!);
            if (existingCard != null) {
              await _daoManager.cardDao.update(card);
            } else {
              await _daoManager.cardDao.insertWithIds(card, userId, bookId);
            }

            // 同步卡片资源
            if (cardData['card_assets'] != null) {
              await _syncCardAssets(cardData['card_assets'], card.id!);
            }
          }

          skip += limit;
          if (cardsData.length < limit) {
            hasMore = false;
          }
        } else {
          hasMore = false;
        }
      }

      Console.log('All cards synced for book: $bookId');
    } catch (e) {
      Console.log('Failed to sync book cards for book $bookId: $e');
      rethrow;
    }
  }

  /// 获取卡片所属的书籍ID
  Future<int?> _getCardBookId(int cardId, Map<String, dynamic> cardData) async {
    try {
      // 首先尝试从API返回的数据中获取
      if (cardData['books'] != null && cardData['books'].isNotEmpty) {
        return cardData['books'][0]['id'];
      }

      // 如果API数据中没有，尝试从本地数据库查询
      final result = await _daoManager.cardDao.db.rawQuery('''
        SELECT book_id FROM user_book_cards
        WHERE card_id = ?
        LIMIT 1
      ''', [cardId]);

      if (result.isNotEmpty) {
        return result.first['book_id'] as int;
      }

      return null;
    } catch (e) {
      Console.log('Failed to get card book ID: $e');
      return null;
    }
  }

  /// 同步资源数据
  Future<void> _syncAssetsData(String userId) async {
    try {
      Console.log('Syncing assets data...');

      int skip = 0;
      const int limit = 50;
      bool hasMore = true;

      while (hasMore) {
        final response = await OxHttp.to.get(
          '/v1/assets',
          queryParameters: {
            'skip': skip,
            'limit': limit,
            'order': '-created_at',
          },
        );

        if (response.statusCode == 200) {
          final List<dynamic> assetsData = response.data['data'] ?? [];

          if (assetsData.isEmpty) {
            hasMore = false;
            break;
          }

          // 这里可以根据需要处理资源数据
          // 目前资源数据已经通过卡片资源关联处理了

          skip += limit;
          if (assetsData.length < limit) {
            hasMore = false;
          }
        } else {
          hasMore = false;
        }
      }

      Console.log('Assets data synced successfully');
    } catch (e) {
      Console.log('Failed to sync assets data: $e');
      rethrow;
    }
  }

  /// 同步学习记录
  Future<void> _syncStudyRecords(String userId) async {
    try {
      Console.log('Syncing study records...');

      // 获取学习计划
      final response = await OxHttp.to.get('/v1/study/my_reviews');
      if (response.statusCode == 200) {
        final List<dynamic> reviewsData = response.data['data'] ?? [];

        for (final reviewData in reviewsData) {
          if (reviewData['schedule_id'] != null) {
            // 这里可以根据需要创建学习记录
            // 由于后端的Schedule和Record模型比较复杂，
            // 我们可以在用户实际学习时再创建记录
          }
        }
      }

      Console.log('Study records synced successfully');
    } catch (e) {
      Console.log('Failed to sync study records: $e');
      rethrow;
    }
  }

  /// 同步书籍计划
  Future<void> _syncBookSchedules(String userId) async {
    try {
      Console.log('Syncing book schedules...');

      final response = await OxHttp.to.get('/v1/book_schedules');
      if (response.statusCode == 200) {
        final List<dynamic> schedulesData = response.data['data'] ?? [];

        for (final _ in schedulesData) {
          // 这里可以根据需要处理书籍计划数据
          // 目前先跳过，因为我们的本地数据库结构稍有不同
        }
      }

      Console.log('Book schedules synced successfully');
    } catch (e) {
      Console.log('Failed to sync book schedules: $e');
      rethrow;
    }
  }

  /// 仅同步在学习的卡片数据
  Future<bool> syncStudyingCardsOnly({String? userId}) async {
    return await syncTable('studying_cards', userId: userId);
  }

  /// 同步在学习书籍的所有卡片数据
  Future<bool> syncStudyingBooksCards({String? userId}) async {
    return await syncTable('studying_books_cards', userId: userId);
  }

  /// 增量同步指定表
  Future<bool> syncTable(String tableName, {String? userId}) async {
    final currentUserId = userId ?? await _getCurrentUserId();
    if (currentUserId == null) return false;

    try {
      switch (tableName) {
        case 'users':
          await _syncUserData(currentUserId);
          break;
        case 'books':
          await _syncBooksData(currentUserId);
          break;
        case 'cards':
          await _syncCardsData(currentUserId);
          break;
        case 'studying_cards':
          await _syncStudyingCardsData(currentUserId);
          break;
        case 'studying_books_cards':
          await _syncStudyingBooksCardsData(currentUserId);
          break;
        case 'assets':
          await _syncAssetsData(currentUserId);
          break;
        case 'study_records':
          await _syncStudyRecords(currentUserId);
          break;
        case 'book_schedules':
          await _syncBookSchedules(currentUserId);
          break;
        default:
          Console.log('Unknown table: $tableName');
          return false;
      }
      return true;
    } catch (e) {
      Console.log('Failed to sync table $tableName: $e');
      return false;
    }
  }
}
